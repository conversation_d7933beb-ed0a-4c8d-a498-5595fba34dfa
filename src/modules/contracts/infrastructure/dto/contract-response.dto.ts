import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ContractStatus, ContractType, EntityType as PrismaEntityType } from '@prisma/client';
import { EntityType } from '../../domain/enums/entity-type.enum';
import { Contract } from '../../domain/entities/contract.entity';

export class ContractVersionDto {
  @ApiProperty()
  versionId: number;

  @ApiProperty()
  filePath: string;

  @ApiProperty()
  uploadedBy: string;

  @ApiPropertyOptional()
  expirationDate?: Date | null;

  @ApiPropertyOptional()
  startDate?: Date | null;

  @ApiProperty()
  createdAt: Date;

  @ApiPropertyOptional()
  downloadUrl?: string;

  @ApiPropertyOptional()
  fileName?: string;

  @ApiPropertyOptional()
  observations?: string;
}

export class ContractResponseDto {
  @ApiProperty()
  uuid: string;

  @ApiProperty({ enum: EntityType })
  entityType: EntityType;

  @ApiProperty()
  entityUuid: string;

  @ApiProperty()
  currentVersion: number;

  @ApiProperty({ enum: ContractStatus })
  status: ContractStatus;

  @ApiProperty({ enum: ContractType })
  contractType: ContractType;

  @ApiProperty()
  createdBy: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiPropertyOptional({ type: [ContractVersionDto] })
  versions?: ContractVersionDto[];

  @ApiPropertyOptional()
  downloadUrl?: string;

  @ApiPropertyOptional()
  fileName?: string;

  @ApiPropertyOptional()
  signed?: boolean;

  static fromEntity(contract: Contract, downloadUrls?: { [versionId: number]: { url: string; fileName: string } }): ContractResponseDto {
    const currentVersion = contract.versions?.find(v => v.versionId === contract.currentVersion);
    const currentDownloadInfo = currentVersion && downloadUrls?.[currentVersion.versionId];

    return {
      uuid: contract.uuid,
      entityType: contract.entityType as EntityType,
      entityUuid: contract.entityUuid,
      currentVersion: contract.currentVersion,
      status: contract.status,
      contractType: contract.contractType,
      createdBy: contract.createdBy,
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
      signed: currentVersion?.signed,
      downloadUrl: currentDownloadInfo?.url || contract.downloadUrl,
      fileName: currentDownloadInfo?.fileName || contract.fileName || currentVersion?.filePath.split('/').pop(),
      versions: contract.versions?.map((version) => {
        const versionDownloadInfo = downloadUrls?.[version.versionId];
        return {
          versionId: version.versionId,
          filePath: version.filePath,
          uploadedBy: version.uploadedBy,
          expirationDate: version.expirationDate || undefined,
          startDate: version.startDate || undefined,
          createdAt: version.createdAt,
          observations: version.observations || undefined,
          downloadUrl: versionDownloadInfo?.url,
          fileName: versionDownloadInfo?.fileName || version.filePath.split('/').pop(),
        };
      }),
    };
  }
} 