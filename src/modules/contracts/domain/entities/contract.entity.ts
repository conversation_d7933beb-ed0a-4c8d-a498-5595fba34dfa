import { ContractStatus, ContractType, ContractVersion, EntityType } from '@prisma/client';

export interface Contract {
  uuid: string;
  entityType: EntityType;
  entityUuid: string;
  currentVersion: number;
  status: ContractStatus;
  contractType: ContractType;
  createdBy: string;
  uptadedBy: string;
  createdAt: Date;
  updatedAt: Date;
  versions?: ContractVersion[];
  updatedBy?: string;
  downloadUrl?: string;
  fileName?: string;
}
