import {
  Controller,
  Get,
  Post,
  UploadedFile,
  UseInterceptors,
  Body,
  Query,
  Param,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  UseGuards,
  BadRequestException,
  Patch,
  Delete,
  Res,
  Request,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
  ApiQuery,
  ApiExcludeController,
} from '@nestjs/swagger';
import { CreateContractUseCase } from '../use-cases/create-contract.use-case';
import { ListContractUseCase } from '../use-cases/list-contract.use-case';
import { DownloadContractUseCase } from '../use-cases/download-contract.use-case';
import { DeleteContractUseCase } from '../use-cases/delete-contract.use-case';
import { UpdateContractUseCase } from '../use-cases/update-contract.use-case';
import { CreateContractDto } from '../../infrastructure/dto/create-contract.dto';
import { ContractResponseDto } from '../../infrastructure/dto/contract-response.dto';
import { Roles } from '@/core/decorators/roles.decorator';
import { Role } from '@/core/enums/role.enum';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@/modules/auth/guards/roles.guard';

@ApiExcludeController() // TODO: Decidir se vamos usar este controller ou continuar usando o SupplierContractController e o CustomerContractController, por enquanto, vamos ocultar este controller
@ApiTags('Contracts')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('admin/contracts')
export class ContractsController {
  constructor(
    private readonly createContractUseCase: CreateContractUseCase,
    private readonly listContractUseCase: ListContractUseCase,
    private readonly downloadContractUseCase: DownloadContractUseCase,
    private readonly deleteContractUseCase: DeleteContractUseCase,
    private readonly updateContractUseCase: UpdateContractUseCase,
  ) { }

  @Post()
  @Roles(Role.DOCUMENT_UPLOADER)
  @HttpCode(HttpStatus.CREATED)
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: CreateContractDto })
  @ApiOperation({ summary: 'Upload de novo contrato' })
  @ApiResponse({ status: 201, description: 'Contrato criado com sucesso' })
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: CreateContractDto,
    @Request() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('Arquivo é obrigatório');
    }
    const contract = await this.createContractUseCase.execute(dto, file, req.user.id);
    return ContractResponseDto.fromEntity(contract);
  }

  @Get()
  @Roles(Role.DOCUMENT_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lista contratos' })
  async list(): Promise<ContractResponseDto[]> {
    const contracts = await this.listContractUseCase.execute({}, 100, 0);
    return contracts.items.map((contract) => ContractResponseDto.fromEntity(contract));
  }

  @Get(':uuid')
  @Roles(Role.DOCUMENT_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Busca contrato por UUID' })
  @ApiResponse({ status: 200, description: 'Contrato encontrado' })
  async getMetadata(
    @Param('uuid', ParseUUIDPipe) uuid: string,
  ): Promise<ContractResponseDto> {
    const contract = await this.listContractUseCase.executeByUuid(uuid);
    if (!contract) {
      throw new BadRequestException('Contrato não encontrado');
    }
    return ContractResponseDto.fromEntity(contract);
  }

  @Get(':uuid/download')
  @Roles(Role.DOCUMENT_VIEWER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Download do contrato' })
  @ApiResponse({ status: 200, description: 'Download do contrato' })
  async download(
    @Param('uuid', ParseUUIDPipe) uuid: string,
    @Query('versionId') versionId?: string,
    @Res() res?: Response,
  ): Promise<void> {
    const { stream, fileName } = await this.downloadContractUseCase.executeStream(
      uuid,
      versionId ? parseInt(versionId) : undefined,
    );

    if (res) {
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      stream.pipe(res);
    }
  }

  @Patch(':uuid')
  @Roles(Role.DOCUMENT_UPLOADER)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Atualiza contrato' })
  @ApiResponse({ status: 200, description: 'Contrato atualizado com sucesso' })
  async update(
    @Param('uuid', ParseUUIDPipe) uuid: string,
    @Body() updateData: any,
  ): Promise<ContractResponseDto> {
    const contract = await this.updateContractUseCase.execute(uuid, updateData);
    return ContractResponseDto.fromEntity(contract);
  }

  @Delete(':uuid')
  @Roles(Role.DOCUMENT_UPLOADER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove contrato' })
  @ApiResponse({ status: 204, description: 'Contrato removido com sucesso' })
  async delete(@Param('uuid', ParseUUIDPipe) uuid: string): Promise<void> {
    await this.deleteContractUseCase.execute(uuid);
  }
} 