import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { IContractRepository } from '../../domain/repositories/contract.repository.interface';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { CreateContractsDto, CreateContractDto } from '../../infrastructure/dto/create-contract.dto';
import { Contract } from '../../domain/entities/contract.entity';
import { ContractStatus, EntityType, ContractType } from '@prisma/client';


@Injectable()
export class CreateContractUseCase {
  constructor(
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,

    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) { }

  async execute(
    dto: CreateContractDto,
    file: Express.Multer.File,
    userId: string
  ): Promise<Contract> {
    if (!file) {
      throw new BadRequestException('Arquivo é obrigatório.');
    }

    try {
      const uuid = uuidv4();
      const versionId = 1;
      const key = `contracts/${dto.entityActualUuid}/${uuid}/v${versionId}/${dto.contractIdentifier || file.originalname}`;

      //await this.storageProvider.upload(file.buffer, file.mimetype, key);

      const contract: Contract = {
        uuid,
        entityType: dto.entityType as EntityType,
        entityUuid: dto.entityActualUuid,
        currentVersion: versionId,
        contractType: dto.contractType as ContractType,
        createdBy: userId,
        uptadedBy: userId,
        status: dto.signed ? ContractStatus.APPROVED : ContractStatus.PENDING,
        createdAt: new Date(),
        updatedAt: new Date(),
        versions: [
          {
            id: uuidv4(),
            versionId,
            filePath: key,
            uploadedAt: new Date(),
            uploadedBy: userId,
            signed: dto.signed || false,
            validatedBy: null,
            validatedAt: null,
            startDate: dto.startDate
              ? new Date(dto.startDate)
              : null,
            expirationDate: dto.expirationDate
              ? new Date(dto.expirationDate)
              : null,
            observations: dto.observations || null,
            contractId: uuid,
            createdAt: new Date(),
          },
        ],
      };

      return await this.contractRepository.create(contract);
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException('Erro ao criar contrato');
    }
  }

  async executeMultiple(
    supplierUuid: string,
    dto: CreateContractsDto,
    files: Express.Multer.File[],
    userId: string
  ): Promise<Contract[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('Pelo menos um arquivo deve ser enviado.');
    }
    if (files.length !== dto.contracts.length) {
      throw new BadRequestException('O número de arquivos não corresponde ao número de metadados de contrato.');
    }

    const results: Contract[] = [];

    try {
      for (const [index, contractDto] of dto.contracts.entries()) {
        const currentFile = files[index];
        const uuid = uuidv4();
        const versionId = 1;
        const key = `contracts/${supplierUuid}/${uuid}/v${versionId}/${contractDto.contractIdentifier || currentFile.originalname}`;

       // await this.storageProvider.upload(currentFile.buffer, currentFile.mimetype, key);

        const contract: Contract = {
          uuid,
          entityType: contractDto.entityType as EntityType,
          entityUuid: contractDto.entityActualUuid, // Usar o novo campo
          currentVersion: versionId,
          contractType: contractDto.contractType as ContractType,
          createdBy: userId,
          uptadedBy: userId,
          status: contractDto.signed ? ContractStatus.APPROVED : ContractStatus.PENDING,
          createdAt: new Date(),
          updatedAt: new Date(),
          versions: [
            {
              id: uuidv4(),
              versionId,
              filePath: key,
              uploadedAt: new Date(),
              uploadedBy: userId,
              signed: contractDto.signed || false,
              validatedBy: null,
              validatedAt: null,
              startDate: contractDto.startDate
                ? new Date(contractDto.startDate)
                : null,
              expirationDate: contractDto.expirationDate
                ? new Date(contractDto.expirationDate)
                : null,
              observations: contractDto.observations || null,
              contractId: uuid,
              createdAt: new Date(),
            },
          ],
        };

        const createdContract = await this.contractRepository.create(contract);
        results.push(createdContract);
      }

      return results;
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException('Erro ao criar contrato');
    }
  }
}
