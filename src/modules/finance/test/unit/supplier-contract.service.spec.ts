import { Test, TestingModule } from '@nestjs/testing';
import { SupplierContractService } from '../../services/supplier-contract.service';
import { CreateContractUseCase } from '@/modules/contracts/application/use-cases/create-contract.use-case';
import { ListContractUseCase } from '@/modules/contracts/application/use-cases/list-contract.use-case';
import { DownloadContractUseCase } from '@/modules/contracts/application/use-cases/download-contract.use-case';
import { DeleteContractUseCase } from '@/modules/contracts/application/use-cases/delete-contract.use-case';
import { UpdateContractUseCase } from '@/modules/contracts/application/use-cases/update-contract.use-case';
import { IContractRepository } from '@/modules/contracts/domain/repositories/contract.repository.interface';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';
import { CreateContractsDto } from '@/modules/contracts/infrastructure/dto/create-contract.dto';
import { EntityType } from '@/modules/contracts/domain/enums/entity-type.enum';
import { ContractUpdatePatchDto } from '@/modules/contracts/infrastructure/dto/create-contract.dto';
import { ContractStatus } from '@prisma/client';
import { NotFoundException } from '@nestjs/common';

describe('SupplierContractService', () => {
  let service: SupplierContractService;
  let createContractUseCase: CreateContractUseCase;
  let listContractUseCase: ListContractUseCase;
  let downloadContractUseCase: DownloadContractUseCase;
  let deleteContractUseCase: DeleteContractUseCase;
  let updateContractUseCase: UpdateContractUseCase;
  let contractRepository: IContractRepository;
  let prisma: PrismaService;
  let validateSupplierActivationUseCase: ValidateSupplierActivationUseCase;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SupplierContractService,
        {
          provide: CreateContractUseCase,
          useValue: { execute: jest.fn(), executeMultiple: jest.fn() },
        },
        {
          provide: ListContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DownloadContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: DeleteContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: UpdateContractUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: 'IContractRepository',
          useValue: {
            findByUuid: jest.fn(),
            createVersion: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: PrismaService,
          useValue: {
            supplier: {
              findUnique: jest.fn(),
            },
          },
        },
        {
          provide: ValidateSupplierActivationUseCase,
          useValue: { execute: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<SupplierContractService>(SupplierContractService);
    createContractUseCase = module.get<CreateContractUseCase>(CreateContractUseCase);
    listContractUseCase = module.get<ListContractUseCase>(ListContractUseCase);
    downloadContractUseCase = module.get<DownloadContractUseCase>(DownloadContractUseCase);
    deleteContractUseCase = module.get<DeleteContractUseCase>(DeleteContractUseCase);
    updateContractUseCase = module.get<UpdateContractUseCase>(UpdateContractUseCase);
    contractRepository = module.get<IContractRepository>('IContractRepository');
    prisma = module.get<PrismaService>(PrismaService);
    validateSupplierActivationUseCase = module.get<ValidateSupplierActivationUseCase>(ValidateSupplierActivationUseCase);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSupplierContract', () => {
    it('should create a supplier contract successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [] as any[];
      const createContractsDto: CreateContractsDto = {
        contracts: [
          {
            contractIdentifier: 'contract.pdf',
            entityType: EntityType.SUPPLIER,
            entityActualUuid: supplierUuid,
          },
        ],
      };
      const mockCreatedContracts = [{ uuid: 'contract-uuid' }] as any[];

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(createContractUseCase, 'executeMultiple').mockResolvedValue(mockCreatedContracts);
      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockResolvedValue(true);

      const result = await service.createSupplierContract(
        supplierUuid,
        files,
        userId,
        createContractsDto,
      );

      expect(result).toEqual(mockCreatedContracts);
      expect(prisma.supplier.findUnique).toHaveBeenCalledWith({
        where: { id: supplierUuid },
      });
      expect(createContractUseCase.executeMultiple).toHaveBeenCalledWith(
        supplierUuid,
        {
          contracts: [
            {
              contractIdentifier: 'contract.pdf',
              entityType: EntityType.SUPPLIER,
              entityActualUuid: supplierUuid,
            },
          ],
        },
        files,
        userId,
      );
      expect(validateSupplierActivationUseCase.execute).toHaveBeenCalled();
    });

    it('should throw NotFoundException if supplier does not exist', async () => {
      const supplierUuid = 'non-existent-uuid';
      const userId = 'user-uuid';
      const files = [] as any[];
      const createContractsDto: CreateContractsDto = { contracts: [] };

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue(null);

      await expect(
        service.createSupplierContract(
          supplierUuid,
          files,
          userId,
          createContractsDto,
        ),
      ).rejects.toThrow(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    });

    it('should still create contract if supplier activation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const userId = 'user-uuid';
      const files = [] as any[];
      const createContractsDto: CreateContractsDto = {
        contracts: [
          {
            contractIdentifier: 'contract.pdf',
            entityType: EntityType.SUPPLIER,
            entityActualUuid: supplierUuid,
          },
        ],
      };
      const mockCreatedContracts = [{ uuid: 'contract-uuid' }] as any[];

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(createContractUseCase, 'executeMultiple').mockResolvedValue(mockCreatedContracts);
      jest.spyOn(validateSupplierActivationUseCase, 'execute').mockRejectedValue(new Error('Activation failed'));

      const result = await service.createSupplierContract(
        supplierUuid,
        files,
        userId,
        createContractsDto,
      );

      expect(result).toEqual(mockCreatedContracts);
    });
  });

  describe('listSupplierContracts', () => {
    it('should return a list of contracts with download URLs', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContracts = [
        {
          uuid: 'contract-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(listContractUseCase, 'execute').mockResolvedValue({ items: mockContracts, total: 1 });
      jest.spyOn(downloadContractUseCase, 'execute').mockResolvedValue({
        url: 'http://example.com/download',
        fileName: 'contract.pdf',
      });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result[0]).toHaveProperty('downloadUrl');
      expect(result[0].downloadUrl).toBe('http://example.com/download');
    });

    it('should return contracts without download URLs if they have no versions', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContracts = [{ uuid: 'contract-uuid', versions: [] }] as any[];

      jest.spyOn(listContractUseCase, 'execute').mockResolvedValue({ items: mockContracts, total: 1 });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return contract without download URL if URL generation fails', async () => {
      const supplierUuid = 'supplier-uuid';
      const mockContracts = [
        {
          uuid: 'contract-uuid',
          currentVersion: 1,
          versions: [{ versionId: 1 }],
        },
      ] as any[];

      jest.spyOn(listContractUseCase, 'execute').mockResolvedValue({ items: mockContracts, total: 1 });
      jest.spyOn(downloadContractUseCase, 'execute').mockImplementation(() => {
        throw new Error('URL generation failed');
      });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result[0]).not.toHaveProperty('downloadUrl');
    });

    it('should return an empty list if no contracts are found', async () => {
      const supplierUuid = 'supplier-uuid';

      jest.spyOn(listContractUseCase, 'execute').mockResolvedValue({ items: [], total: 0 });

      const result = await service.listSupplierContracts(supplierUuid);

      expect(result).toEqual([]);
    });
  });

  describe('getSupplierContract', () => {
    it('should return a contract if found', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
      } as any;

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);

      const result = await service.getSupplierContract(supplierUuid, contractUuid);

      expect(result).toEqual(mockContract);
    });

    it('should throw NotFoundException if supplier does not exist', async () => {
      const supplierUuid = 'non-existent-uuid';
      const contractUuid = 'contract-uuid';

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue(null);

      await expect(
        service.getSupplierContract(supplierUuid, contractUuid),
      ).rejects.toThrow(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    });

    it('should return null if contract is not found', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'non-existent-contract';

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(null);

      const result = await service.getSupplierContract(supplierUuid, contractUuid);

      expect(result).toBeNull();
    });

    it('should return null if contract does not belong to supplier', async () => {
      const supplierUuid = 'supplier-uuid';
      const anotherSupplierUuid = 'another-supplier-uuid';
      const contractUuid = 'contract-uuid';
      const mockContract = {
        uuid: contractUuid,
        entityUuid: anotherSupplierUuid,
      } as any;

      jest.spyOn(prisma.supplier, 'findUnique').mockResolvedValue({} as any);
      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);

      const result = await service.getSupplierContract(supplierUuid, contractUuid);

      expect(result).toBeNull();
    });
  });

  describe('deleteSupplierContract', () => {
    it('should delete a contract successfully', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
      } as any;

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest.spyOn(deleteContractUseCase, 'execute').mockResolvedValue(undefined);

      await service.deleteSupplierContract(supplierUuid, contractUuid);

      expect(contractRepository.findByUuid).toHaveBeenCalledWith(contractUuid);
      expect(deleteContractUseCase.execute).toHaveBeenCalledWith(contractUuid);
    });

    it('should throw NotFoundException if contract is not found', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'non-existent-contract';

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(null);

      await expect(
        service.deleteSupplierContract(supplierUuid, contractUuid),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });

    it('should throw NotFoundException if contract does not belong to supplier', async () => {
      const supplierUuid = 'supplier-uuid';
      const anotherSupplierUuid = 'another-supplier-uuid';
      const contractUuid = 'contract-uuid';
      const mockContract = {
        uuid: contractUuid,
        entityUuid: anotherSupplierUuid,
      } as any;

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);

      await expect(
        service.deleteSupplierContract(supplierUuid, contractUuid),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });
  });

  describe('updateSupplierContract', () => {
    it('should update a contract successfully when signed is true', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = { signed: true };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
        createdBy: 'user-uuid',
        currentVersion: 1,
        versions: [{ versionId: 1, filePath: 'path/to/file' }],
      } as any;
      const mockUpdatedContract = { ...mockContract, status: ContractStatus.APPROVED };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest.spyOn(contractRepository, 'update').mockResolvedValue(mockUpdatedContract);

      const result = await service.updateSupplierContract(
        supplierUuid,
        contractUuid,
        patch,
      );

      expect(contractRepository.findByUuid).toHaveBeenCalledWith(contractUuid);
      expect(contractRepository.createVersion).toHaveBeenCalledWith(
        contractUuid,
        expect.objectContaining({ signed: true }),
      );
      expect(contractRepository.update).toHaveBeenCalledWith(contractUuid, {
        status: ContractStatus.APPROVED,
      });
      expect(result).toEqual(mockUpdatedContract);
    });

    it('should update a contract successfully when signed is false', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = { signed: false };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: supplierUuid,
        createdBy: 'user-uuid',
        currentVersion: 1,
        versions: [{ versionId: 1, filePath: 'path/to/file' }],
      } as any;
      const mockUpdatedContract = { ...mockContract, status: ContractStatus.REJECTED };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);
      jest.spyOn(contractRepository, 'update').mockResolvedValue(mockUpdatedContract);

      const result = await service.updateSupplierContract(
        supplierUuid,
        contractUuid,
        patch,
      );

      expect(contractRepository.update).toHaveBeenCalledWith(contractUuid, {
        status: ContractStatus.REJECTED,
      });
      expect(result).toEqual(mockUpdatedContract);
    });

    it('should throw NotFoundException if contract is not found', async () => {
      const supplierUuid = 'supplier-uuid';
      const contractUuid = 'non-existent-contract';
      const patch: ContractUpdatePatchDto = { signed: true };

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(null);

      await expect(
        service.updateSupplierContract(supplierUuid, contractUuid, patch),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });

    it('should throw NotFoundException if contract does not belong to supplier', async () => {
      const supplierUuid = 'supplier-uuid';
      const anotherSupplierUuid = 'another-supplier-uuid';
      const contractUuid = 'contract-uuid';
      const patch: ContractUpdatePatchDto = { signed: true };
      const mockContract = {
        uuid: contractUuid,
        entityUuid: anotherSupplierUuid,
      } as any;

      jest.spyOn(contractRepository, 'findByUuid').mockResolvedValue(mockContract);

      await expect(
        service.updateSupplierContract(supplierUuid, contractUuid, patch),
      ).rejects.toThrow('Contrato não encontrado para este fornecedor');
    });
  });
}); 