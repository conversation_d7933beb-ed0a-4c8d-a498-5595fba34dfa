import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { CreateContractUseCase } from '@/modules/contracts/application/use-cases/create-contract.use-case';
import { Contract } from '@/modules/contracts/domain/entities/contract.entity';
import { CreateContractsDto } from '@/modules/contracts/infrastructure/dto/create-contract.dto';
import { ListContractUseCase } from '@/modules/contracts/application/use-cases/list-contract.use-case';
import { DownloadContractUseCase } from '@/modules/contracts/application/use-cases/download-contract.use-case';
import { DeleteContractUseCase } from '@/modules/contracts/application/use-cases/delete-contract.use-case';
import { IContractRepository } from '@/modules/contracts/domain/repositories/contract.repository.interface';
import { UpdateContractUseCase } from '@/modules/contracts/application/use-cases/update-contract.use-case';
import { ContractUpdatePatchDto } from '@/modules/contracts/infrastructure/dto/create-contract.dto';
import { ContractStatus, ContractType } from '@prisma/client';
import { EntityType } from '@/modules/contracts/domain/enums/entity-type.enum';
import { PrismaService } from '../../../infrastructure/prisma/prisma.service';
import { ValidateSupplierActivationUseCase } from '@/core/application/use-cases/supplier/validate-supplier-activation.use-case';

@Injectable()
export class SupplierContractService {
  constructor(
    @Inject(CreateContractUseCase)
    private readonly createContractUseCase: CreateContractUseCase,
    @Inject(ListContractUseCase)
    private readonly listContractUseCase: ListContractUseCase,
    private readonly downloadContractUseCase: DownloadContractUseCase,
    @Inject(DeleteContractUseCase)
    private readonly deleteContractUseCase: DeleteContractUseCase,
    private readonly prisma: PrismaService,
    private readonly validateSupplierActivationUseCase: ValidateSupplierActivationUseCase,
    @Inject('IContractRepository')
    private readonly contractRepository: IContractRepository,
    @Inject(UpdateContractUseCase)
    private readonly updateContractUseCase: UpdateContractUseCase,
  ) { }

  async createSupplierContract(
    supplierUuid: string,
    files: Express.Multer.File[],
    userId: string,
    createContractsDto: CreateContractsDto,
  ): Promise<Contract[]> {
    const supplierExists = await this.prisma.supplier.findUnique({
      where: { id: supplierUuid },
    });
    if (!supplierExists) {
      throw new NotFoundException(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    }

    const populatedContracts = createContractsDto.contracts.map((contract) => ({
      ...contract,
      entityActualUuid: supplierUuid,
    }));

    const populatedDto: CreateContractsDto = {
      ...createContractsDto,
      contracts: populatedContracts,
    };

    const contracts = await this.createContractUseCase.executeMultiple(
      supplierUuid,
      populatedDto,
      files,
      userId,
    );

    // Tentar ativar o supplier automaticamente após criar contrato
    console.log(
      `[SupplierContractService] Tentando ativar supplier ${supplierUuid} após criar contrato`,
    );
    try {
      const activated = await this.validateSupplierActivationUseCase.execute(
        supplierUuid,
        userId,
      );
      console.log(
        `[SupplierContractService] Resultado da ativação após criar contrato: ${activated}`,
      );
    } catch (error) {
      console.error(
        '[SupplierContractService] Erro ao tentar ativar supplier após criar contrato:',
        error,
      );
    }

    return contracts;
  }

  async listSupplierContracts(supplierUuid: string): Promise<Contract[]> {
    const { items } = await this.listContractUseCase.execute(
      {
        entityType: EntityType.SUPPLIER,
        entityUuid: supplierUuid,
      },
      100,
      0,
    );

    // Gerar URLs de download para todos os contratos
    const contractsWithUrls = await Promise.all(
      items.map(async (contract) => {
        if (contract.versions && contract.versions.length > 0) {
          try {
            // Gerar URL para a versão atual
            const currentVersion = contract.versions.find(v => v.versionId === contract.currentVersion);
            if (currentVersion) {
              const { url, fileName } = await this.downloadContractUseCase.execute(contract.uuid);
              return {
                ...contract,
                downloadUrl: url,
                fileName: fileName,
              };
            }
          } catch (error) {
            console.error(`Erro ao gerar URL de download para contrato ${contract.uuid}:`, error);
          }
        }
        return contract;
      })
    );

    return contractsWithUrls;
  }

  async getSupplierContract(supplierUuid: string, contractUuid: string): Promise<Contract | null> {
    const supplierExists = await this.prisma.supplier.findUnique({
      where: { id: supplierUuid },
    });
    if (!supplierExists) {
      throw new NotFoundException(
        `Fornecedor com UUID ${supplierUuid} não encontrado.`,
      );
    }

    const contract = await this.contractRepository.findByUuid(contractUuid);

    if (!contract || contract.entityUuid !== supplierUuid) {
      return null;
    }

    return contract;
  }

  async deleteSupplierContract(supplierUuid: string, contractUuid: string): Promise<void> {
    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract || contract.entityUuid !== supplierUuid) {
      throw new NotFoundException('Contrato não encontrado para este fornecedor');
    }
    await this.deleteContractUseCase.execute(contractUuid);
  }

  async updateSupplierContract(
    supplierUuid: string,
    contractUuid: string,
    patch: ContractUpdatePatchDto,
  ): Promise<Contract> {
    const contract = await this.contractRepository.findByUuid(contractUuid);
    if (!contract || contract.entityUuid !== supplierUuid) {
      throw new NotFoundException('Contrato não encontrado para este fornecedor');
    }

    const currentVersion = contract.versions?.find(v => v.versionId === contract.currentVersion);

    const versionData = {
      uploadedBy: contract.createdBy,
      filePath: currentVersion?.filePath || '',
      signed: patch.signed !== undefined ? patch.signed : currentVersion?.signed || false,
      startDate: patch.startDate ? new Date(patch.startDate) : currentVersion?.startDate || undefined,
      expirationDate: patch.expirationDate ? new Date(patch.expirationDate) : currentVersion?.expirationDate || undefined,
      observations: patch.observations !== undefined ? patch.observations : currentVersion?.observations || undefined,
    };

    // New version if there are changes
    if (patch.signed !== undefined || patch.startDate || patch.expirationDate || patch.observations !== undefined) {
      await this.contractRepository.createVersion(contractUuid, versionData);
    }

    // Prepare data for contract update
    const contractUpdateData: Partial<Contract> = {};

    if (patch.contractType !== undefined) {
      contractUpdateData.contractType = patch.contractType;
    }

    // Status always based on the signed field
    if (patch.signed !== undefined) {
      contractUpdateData.status = patch.signed ? ContractStatus.APPROVED : ContractStatus.REJECTED;
    }

    return this.contractRepository.update(contractUuid, contractUpdateData);
  }
} 